{"name": "concurrently", "version": "9.1.2", "description": "Run commands concurrently", "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "main": "index.js", "types": "index.d.ts", "type": "commonjs", "bin": {"concurrently": "./dist/bin/concurrently.js", "conc": "./dist/bin/concurrently.js"}, "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/open-cli-tools/concurrently.git"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "lodash": "^4.17.21", "rxjs": "^7.8.1", "shell-quote": "^1.8.1", "supports-color": "^8.1.1", "tree-kill": "^1.2.2", "yargs": "^17.7.2"}, "devDependencies": {"@hirez_io/observer-spy": "^2.2.0", "@jest/types": "^29.6.3", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.6", "@types/lodash": "^4.17.7", "@types/node": "^18.19.50", "@types/shell-quote": "^1.7.5", "@types/supports-color": "^8.1.3", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "coveralls-next": "^4.2.1", "ctrlc-wrapper": "^0.0.4", "esbuild": "~0.23.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^9.1.5", "jest": "^29.7.0", "jest-create-mock-instance": "^2.0.0", "lint-staged": "^15.2.10", "prettier": "^3.0.3", "safe-publish-latest": "^2.0.0", "string-argv": "^0.3.2", "typescript": "~5.2.2"}, "files": ["dist", "index.js", "index.d.ts", "index.mjs", "index.d.mts", "!**/fixtures", "!**/*.spec.js", "!**/*.spec.d.ts", "docs"], "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "scripts": {"build": "tsc --build", "postbuild": "chmod +x dist/bin/concurrently.js", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "format:fix": "pnpm run format --write", "lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "lint:fix": "pnpm run lint --fix", "report-coverage": "cat coverage/lcov.info | coveralls", "test": "jest --selectProjects unit", "test:smoke": "jest --selectProjects smoke"}}