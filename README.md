# Todo List Manager

Ứng dụng quản lý Todo List được xây dựng với <PERSON>act, Redux Toolkit, React Router và Tailwind CSS.

## 🚀 Tính năng

- ✅ Thêm, x<PERSON><PERSON>, cập nhật todo
- ✅ Đánh dấu hoàn thành/chưa hoàn thành
- ✅ Phân loại độ ưu tiên (High, Medium, Low)
- ✅ Xem chi tiết từng todo
- ✅ Giao diện responsive với Tailwind CSS
- ✅ State management với Redux Toolkit
- ✅ API simulation với JSON Server
- ✅ Loading states và error handling

## 🛠️ Công nghệ sử dụng

- **Frontend**: React 19, Vite
- **State Management**: Redux Toolkit, React Redux
- **Routing**: React Router DOM
- **Styling**: Tailwind CSS
- **API**: JSON Server, Axios
- **Build Tool**: Vite

## 📦 Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd to-do-list
```

2. Cài đặt dependencies:
```bash
npm install
npm install -D @tailwindcss/postcss
```

3. Khởi chạy JSON Server (Terminal 1):
```bash
npm run server
```

4. Khởi chạy React app (Terminal 2):
```bash
npm run dev
```

Hoặc chạy cả hai cùng lúc:
```bash
npm run dev:full
```

## 🌐 URLs

- **React App**: http://localhost:5173
- **JSON Server API**: http://localhost:3001
- **API Endpoints**: http://localhost:3001/todos

## 📁 Cấu trúc thư mục

```
src/
├── components/          # React components
│   ├── TodoCard.jsx    # Component hiển thị todo card
│   ├── TodoForm.jsx    # Form thêm todo mới
│   └── LoadingSpinner.jsx # Loading spinner component
├── hooks/              # Custom hooks
│   └── useTodoAPI.js   # Hook quản lý API calls
├── pages/              # Page components
│   ├── HomePage.jsx    # Trang chủ hiển thị danh sách todos
│   └── TodoDetailPage.jsx # Trang chi tiết todo
├── store/              # Redux store
│   ├── store.js        # Redux store configuration
│   └── todosSlice.js   # Todos slice với async thunks
├── App.jsx             # Main App component
└── main.jsx           # Entry point
```

## 🔧 Scripts

- `npm run dev` - Khởi chạy development server
- `npm run server` - Khởi chạy JSON Server
- `npm run dev:full` - Chạy cả server và client
- `npm run build` - Build production
- `npm run preview` - Preview production build

## 📝 API Endpoints

### GET /todos
Lấy danh sách tất cả todos

### POST /todos
Thêm todo mới
```json
{
  "title": "Todo title",
  "description": "Todo description",
  "priority": "high|medium|low",
  "completed": false
}
```

### PATCH /todos/:id
Cập nhật todo
```json
{
  "completed": true
}
```

### DELETE /todos/:id
Xóa todo

## 🎯 Cách sử dụng

1. **Thêm Todo**: Điền form bên trái và click "Add Todo"
2. **Xem chi tiết**: Click nút "View" trên todo card
3. **Đánh dấu hoàn thành**: Click nút "Complete" hoặc "Undo"
4. **Xóa Todo**: Click nút "Delete" và xác nhận

## 🔄 Redux State Structure

```javascript
{
  todos: {
    items: [],      // Danh sách todos
    loading: false, // Trạng thái loading
    error: null     // Thông báo lỗi
  }
}
```

## 🎨 Tailwind CSS Classes

Ứng dụng sử dụng Tailwind CSS với:
- Grid layout responsive
- Color-coded priority levels
- Hover effects và transitions
- Loading states
- Error handling UI

## 🚀 Deployment

1. Build production:
```bash
npm run build
```

2. Deploy thư mục `dist` lên hosting service

## 📄 License

MIT License
