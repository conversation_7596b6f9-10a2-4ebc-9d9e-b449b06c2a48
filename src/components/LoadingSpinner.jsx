const LoadingSpinner = ({ size = 'medium', text = 'Loading...' }) => {
  const getSpinnerClass = () => {
    switch (size) {
      case 'small':
        return 'spinner spinner-small'
      case 'large':
        return 'spinner'
      default:
        return 'spinner'
    }
  }

  return (
    <div className="loading-spinner">
      <div className={getSpinnerClass()}></div>
      {text && <p>{text}</p>}
    </div>
  )
}

export default LoadingSpinner
