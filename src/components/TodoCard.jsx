import { Link } from 'react-router-dom'

const TodoCard = ({ todo, onDelete, onToggleComplete }) => {
  const getCardClasses = () => {
    let classes = 'todo-card'
    if (todo.priority) {
      classes += ` priority-${todo.priority}`
    }
    if (todo.completed) {
      classes += ' completed'
    }
    return classes
  }

  const getTitleClasses = () => {
    return todo.completed ? 'todo-title completed' : 'todo-title'
  }

  const getDescriptionClasses = () => {
    return todo.completed ? 'todo-description completed' : 'todo-description'
  }

  const getPriorityBadgeClasses = () => {
    return `priority-badge priority-${todo.priority || 'medium'}`
  }

  return (
    <div className={getCardClasses()}>
      <div className="todo-header">
        <h3 className={getTitleClasses()}>
          {todo.title}
        </h3>
        <span className={getPriorityBadgeClasses()}>
          {(todo.priority || 'normal').toUpperCase()}
        </span>
      </div>
      
      <p className={getDescriptionClasses()}>
        {todo.description || 'No description provided.'}
      </p>
      
      <div className="todo-actions">
        <button
          onClick={() => onToggleComplete(todo)}
          className={`btn ${todo.completed ? 'btn-secondary' : 'btn-success'}`}
        >
          {todo.completed ? 'Undo' : 'Complete'}
        </button>
        
        <Link
          to={`/todos/${todo.id}`}
          className="btn btn-primary"
        >
          View
        </Link>
        
        <button
          onClick={() => onDelete(todo.id)}
          className="btn btn-danger"
        >
          Delete
        </button>
      </div>
      
      <div className="todo-meta">
        Created: {new Date(todo.createdAt).toLocaleDateString()}
      </div>
    </div>
  )
}

export default TodoCard
