/* Reset và Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Layout Grid */
.main-layout {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .main-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Form Styles */
.todo-form {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.todo-form h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.25rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-success {
  background-color: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #229954;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

.btn-full {
  width: 100%;
}

/* Todos Grid */
.todos-section h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.todos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .todos-grid {
    grid-template-columns: 1fr;
  }
}

/* Todo Card */
.todo-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 4px solid #bdc3c7;
}

.todo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.todo-card.priority-high {
  border-left-color: #e74c3c;
}

.todo-card.priority-medium {
  border-left-color: #f39c12;
}

.todo-card.priority-low {
  border-left-color: #27ae60;
}

.todo-card.completed {
  opacity: 0.7;
}

.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.todo-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.todo-title.completed {
  text-decoration: line-through;
  color: #7f8c8d;
}

.priority-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-high {
  background-color: #fadbd8;
  color: #c0392b;
}

.priority-medium {
  background-color: #fdeaa7;
  color: #d68910;
}

.priority-low {
  background-color: #d5f4e6;
  color: #196f3d;
}

.todo-description {
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.todo-description.completed {
  text-decoration: line-through;
  color: #999;
}

.todo-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.todo-meta {
  font-size: 0.8rem;
  color: #7f8c8d;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner-small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

/* Error Alert */
.error-alert {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #f5c6cb;
}

.error-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #721c24;
  padding: 0;
  margin-left: 1rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #7f8c8d;
}

.empty-state .icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #5d6d7e;
}

/* Detail Page */
.detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem 0;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #3498db;
  text-decoration: none;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.back-link:hover {
  color: #2980b9;
}

.detail-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.detail-title {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.detail-title.completed {
  text-decoration: line-through;
  color: #7f8c8d;
}

.status-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid;
}

.status-completed {
  background-color: #d5f4e6;
  color: #196f3d;
  border-color: #a9dfbf;
}

.status-pending {
  background-color: #fdeaa7;
  color: #d68910;
  border-color: #f7dc6f;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h2 {
  margin-bottom: 0.75rem;
  color: #2c3e50;
  font-size: 1.25rem;
}

.detail-description {
  color: #555;
  line-height: 1.6;
  font-size: 1rem;
}

.detail-description.completed {
  text-decoration: line-through;
  color: #999;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
}

.metadata-item {
  display: flex;
  flex-direction: column;
}

.metadata-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #7f8c8d;
  margin-bottom: 0.25rem;
}

.metadata-value {
  color: #2c3e50;
  font-weight: 500;
}

.detail-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Not Found */
.not-found {
  text-align: center;
  padding: 3rem 1rem;
}

.not-found .icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.not-found h2 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.not-found p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
}
