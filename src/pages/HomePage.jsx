import { useTodoAPI } from '../hooks/useTodoAPI'
import TodoCard from '../components/TodoCard'
import TodoForm from '../components/TodoForm'
import LoadingSpinner from '../components/LoadingSpinner'

const HomePage = () => {
  const { 
    todos, 
    loading, 
    error, 
    addTodo, 
    deleteTodo, 
    toggleComplete, 
    clearError 
  } = useTodoAPI()

  const handleAddTodo = async (todoData) => {
    return await addTodo(todoData)
  }

  const handleDeleteTodo = async (todoId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      return await deleteTodo(todoId)
    }
  }

  const handleToggleComplete = async (todo) => {
    return await toggleComplete(todo)
  }

  if (loading && todos.length === 0) {
    return (
      <div className="detail-page">
        <div className="container">
          <LoadingSpinner size="large" text="Loading todos..." />
        </div>
      </div>
    )
  }

  return (
    <div className="detail-page">
      <div className="container">
        <header className="header">
          <h1>Todo List Manager</h1>
          <p>Manage your tasks efficiently with Redux & React</p>
        </header>

        {error && (
          <div className="error-alert">
            <span>Error: {error}</span>
            <button 
              onClick={clearError}
              className="error-close"
            >
              ×
            </button>
          </div>
        )}

        <div className="main-layout">
          {/* Form Section */}
          <div>
            <TodoForm onSubmit={handleAddTodo} loading={loading} />
          </div>

          {/* Todos Grid Section */}
          <div className="todos-section">
            <h2>
              Your Todos ({todos.length})
              {loading && (
                <LoadingSpinner size="small" text="" />
              )}
            </h2>

            {todos.length === 0 ? (
              <div className="empty-state">
                <div className="icon">📝</div>
                <h3>No todos yet</h3>
                <p>Add your first todo to get started!</p>
              </div>
            ) : (
              <div className="todos-grid">
                {todos.map((todo) => (
                  <TodoCard
                    key={todo.id}
                    todo={todo}
                    onDelete={handleDeleteTodo}
                    onToggleComplete={handleToggleComplete}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
