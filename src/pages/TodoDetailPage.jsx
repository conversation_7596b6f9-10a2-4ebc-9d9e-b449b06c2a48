import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { useTodoAPI } from '../hooks/useTodoAPI'
import LoadingSpinner from '../components/LoadingSpinner'

const TodoDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { loading, deleteTodo, toggleComplete } = useTodoAPI()
  
  const todo = useSelector(state => 
    state.todos.items.find(todo => todo.id.toString() === id)
  )

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      const result = await deleteTodo(id)
      if (result.success) {
        navigate('/')
      }
    }
  }

  const handleToggleComplete = async () => {
    await toggleComplete(todo)
  }

  if (loading && !todo) {
    return (
      <div className="detail-page">
        <div className="container">
          <LoadingSpinner size="large" text="Loading todo..." />
        </div>
      </div>
    )
  }

  if (!todo) {
    return (
      <div className="detail-page">
        <div className="container">
          <div className="not-found">
            <div className="icon">❌</div>
            <h2>Todo Not Found</h2>
            <p>The todo you're looking for doesn't exist.</p>
            <Link to="/" className="btn btn-primary">
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const getPriorityBadgeClasses = () => {
    return `priority-badge priority-${todo.priority || 'medium'}`
  }

  const getStatusBadgeClasses = () => {
    return `status-badge ${todo.completed ? 'status-completed' : 'status-pending'}`
  }

  const getTitleClasses = () => {
    return todo.completed ? 'detail-title completed' : 'detail-title'
  }

  const getDescriptionClasses = () => {
    return todo.completed ? 'detail-description completed' : 'detail-description'
  }

  return (
    <div className="detail-page">
      <div className="container">
        {/* Navigation */}
        <Link to="/" className="back-link">
          ← Back to Todos
        </Link>

        {/* Todo Detail Card */}
        <div className="detail-card">
          <div className="detail-header">
            <div>
              <h1 className={getTitleClasses()}>
                {todo.title}
              </h1>
              <div className="status-badges">
                <span className={getPriorityBadgeClasses()}>
                  {(todo.priority || 'normal').toUpperCase()} Priority
                </span>
                <span className={getStatusBadgeClasses()}>
                  {todo.completed ? 'Completed' : 'Pending'}
                </span>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="detail-section">
            <h2>Description</h2>
            <p className={getDescriptionClasses()}>
              {todo.description || 'No description provided.'}
            </p>
          </div>

          {/* Metadata */}
          <div className="detail-section">
            <h2>Details</h2>
            <div className="metadata-grid">
              <div className="metadata-item">
                <span className="metadata-label">Created At:</span>
                <span className="metadata-value">
                  {new Date(todo.createdAt).toLocaleString()}
                </span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Status:</span>
                <span className="metadata-value">
                  {todo.completed ? 'Completed' : 'In Progress'}
                </span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Priority Level:</span>
                <span className="metadata-value">
                  {(todo.priority || 'Normal').charAt(0).toUpperCase() + (todo.priority || 'Normal').slice(1)}
                </span>
              </div>
              <div className="metadata-item">
                <span className="metadata-label">Todo ID:</span>
                <span className="metadata-value">#{todo.id}</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="detail-actions">
            <button
              onClick={handleToggleComplete}
              disabled={loading}
              className={`btn ${todo.completed ? 'btn-secondary' : 'btn-success'}`}
            >
              {loading ? 'Updating...' : (todo.completed ? 'Mark as Pending' : 'Mark as Complete')}
            </button>
            
            <button
              onClick={handleDelete}
              disabled={loading}
              className="btn btn-danger"
            >
              {loading ? 'Deleting...' : 'Delete Todo'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TodoDetailPage
